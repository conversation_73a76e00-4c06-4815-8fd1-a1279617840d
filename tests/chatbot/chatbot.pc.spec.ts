import { test, expect } from '@playwright/test'
import {
  askChatBotPC
} from '../utils/helpers'

// const CHATBOT_URL = 'https://chat.dev.pnl-ai-concierge.com/chatbot?tenantId=miyawaka'
const CHATBOT_URL = 'https://chat.dev.pnl-ai-concierge.com/chatbot?tenantId=pnl_playground'

test.describe('宮若市AIコンシェルジュ - チャットボット画面', () => {

  test.beforeEach(async ({ page }) => {
    await page.goto(CHATBOT_URL)
    await page.waitForTimeout(10000)
    // チャットボットアイコン（猫アイコン）をクリックして開く
    // await page.locator('.avatar-container').click()
    await page.locator('.chat-bubble-container').click()
  })

  test('レイアウト表示', async ({ page }) => {
    await expect(page.getByText('こんにちは！宮若市AIコンシェルジュです！')).toBeVisible({ timeout: 15000 })
  })

  test('メッセージ送信 → 回答とアンケート表示 → アンケートの「はい」ボタンを押せる', async ({ page }) => {
    // 利用者用チャットボットを開いて、確認メッセージを送信
    await askChatBotPC(page)

    // アンケート表示確認
    await expect(page.getByText('お役に立てましたか？')).toBeVisible({ timeout: 8000 })
  
    // "はい" と "いいえ" の div ボタン確認（順不同で出る可能性もあるので `hasText` で確認）
    const surveyButtons = page.locator('div.aiko-cursor-pointer')
    await expect(surveyButtons.filter({ hasText: 'はい' })).toBeVisible()
    await expect(surveyButtons.filter({ hasText: 'いいえ' })).toBeVisible()

    // 「はい」ボタンをクリック（divタグ）
    await surveyButtons.filter({ hasText: 'はい' }).click()
    // 次の質問表示を確認
    await expect(page.getByText('他にもお困りのことはありますか？')).toBeVisible({ timeout: 8000 })
  })

  test('閉じるボタンでチャットを閉じる', async ({ page }) => {
    // 表示中のチャットウィンドウ（bounceInUp）を明示的に選択
    const openChatWindow = page.locator('.aiko-chat-window.animate__bounceInUp')
    await expect(openChatWindow).toBeVisible({ timeout: 10000 })
  
    // 閉じる ✖️ ボタンのSVGを取得（最初の .aiko-w-20 内）
    const closeIcon = openChatWindow.locator('.aiko-w-20 svg').first()
  
    // 念のため少し待ってからクリック（スタイル反映待ち）
    await page.waitForTimeout(300)
    await closeIcon.click({ force: true })
  
    // チャットが閉じたことを確認（"こんにちは！" が見えなくなる）
    await expect(page.getByText('こんにちは！宮若市AIコンシェルジュです！')).not.toBeVisible({ timeout: 5000 })
  })

  test('Refresh → キャンセルで戻る → チャットをクリアする', async ({ page }) => {
    await askChatBotPC(page)
  
    const openChatWindow = page.locator('.aiko-chat-window.animate__bounceInUp')
    await expect(openChatWindow).toBeVisible({ timeout: 5000 })
  
    // ヘッダー内の .aiko-w-20 の中から Reset chat アイコンを取得
    const refreshIcon = openChatWindow.locator('.aiko-w-20 svg[title="Reset chat"]').first()
    await refreshIcon.waitFor({ state: 'visible', timeout: 5000 })
    await refreshIcon.click()
  
    await page.getByRole('button', { name: 'キャンセル' }).click()
    await expect(page.getByText('こんにちは！宮若市AIコンシェルジュです！')).toBeVisible({ timeout: 8000 })
    await expect(page.locator('text=お役に立てましたか？')).toBeVisible()
  
    await refreshIcon.click()
    await page.getByRole('button', { name: 'チャットをクリアする' }).click()
    await expect(page.getByText('こんにちは！宮若市AIコンシェルジュです！')).toBeVisible({ timeout: 8000 })
    await expect(page.locator('text=お役に立てましたか？')).not.toBeVisible()
  })

  test('チャットボットの拡大・縮小確認（PCのみ）', async ({ page }) => {
    const openChatWindow = page.locator('.aiko-chat-window').filter({
      has: page.locator('text=宮若市AIコンシェルジュ'),
    }).first()
  
    await expect(openChatWindow).toBeVisible({ timeout: 5000 })
  
    const fullScreenIcon = openChatWindow.locator('.aiko-w-20 svg[title="Full screen"], svg[title="Exit full screen"]').first()
    await fullScreenIcon.waitFor({ state: 'visible', timeout: 5000 })
  
    // 拡大クリック
    await fullScreenIcon.click()
    await expect(openChatWindow).toHaveClass(/fullscreen-chat/)
  
    // 再取得：Exit full screen アイコンが出現後、縮小
    await page.waitForTimeout(500)
    await fullScreenIcon.click()
    await expect(openChatWindow).toHaveClass(/vr-chat-window/)
  })
})
