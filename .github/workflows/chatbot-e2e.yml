name: <PERSON>2<PERSON>bot Test

on:
  schedule:
    # - cron: '0 22 * * *'  # JST 07:00（= UTC 22:00）
    - cron: '0 7 * * *'  # JST 16:00（= UTC 07:00）
  workflow_dispatch:

jobs:
  chatbot-e2e:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Install dependencies
        run: pnpm install

      - name: Install browsers
        run: pnpm exec playwright install --with-deps

      - name: Run PC test
        id: run-pc
        continue-on-error: true
        run: |
          pnpm exec playwright test tests/chatbot/chatbot.pc.spec.ts --config=playwright.chatbot.config.ts
          echo "pc_status=$?" >> $GITHUB_ENV

      - name: Run Mobile test
        id: run-mobile
        continue-on-error: true
        run: |
          pnpm exec playwright test tests/chatbot/chatbot.mobile.spec.ts --config=playwright.chatbot.config.ts
          echo "mobile_status=$?" >> $GITHUB_ENV

      - name: Check test results and fail if any failed
        if: always()
        run: |
          echo "PC Status: $pc_status"
          echo "Mobile Status: $mobile_status"
          if [ "$pc_status" != "0" ] || [ "$mobile_status" != "0" ]; then
            echo "At least one test failed."
            exit 1
          else
            echo "All tests passed."
          fi

      - name: Slack通知（失敗時）
        if: failure()
        run: |
          curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"❌ チャットボットE2Eテストが失敗しました。GitHub Actionsログを確認してください。"}' \
            ${{ secrets.SLACK_WEBHOOK_URL }}