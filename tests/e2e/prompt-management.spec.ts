import { test, expect } from '@playwright/test'
import {
  commonLogin,
  navigateAndWait
} from '../utils/helpers'

test.describe.serial('PNL管理者ユーザ管理機能テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=プロンプト管理'))
  })

  // No.1: レイアウト表示（プロンプト管理画面）
  test('レイアウト表示', async ({ page }) => {
    await expect(page.getByText('プロンプト管理').nth(1)).toBeVisible()
    await expect(page.getByText('各プロンプトタイプの設定を管理します')).toBeVisible()

    const promptLabels = [
        'Basic Query',
        'RAG Query',
        'Analyze',
        'Refine',
        'Input Checker',
        'Reply Analyze',
        'Reference URL',
        'Context Useful',
        'Weather Query',
        'Query Translate',
        'Answer Translate'
      ]
    
    for (const label of promptLabels) {
        const cardTitle = page.getByRole('heading', { name: label, exact: true })
        await expect(cardTitle).toBeVisible()
    }
  })

  test.describe('プロンプト設定 - 各カテゴリ操作', () => {
    const promptTypes = [
      { label: 'Basic Query', type: 1 },
      { label: 'RAG Query', type: 2 },
      { label: 'Analyze', type: 3 },
      { label: 'Refine', type: 4 },
      { label: 'Input Checker', type: 5 },
      { label: 'Reply Analyze', type: 6 },
      { label: 'Reference URL', type: 10 },
      { label: 'Context Useful', type: 11 },
      { label: 'Weather Query', type: 101 },
      { label: 'Query Translate', type: 121 },
      { label: 'Answer Translate', type: 122 }
    ]
    // const promptTypes = [
    //     { label: 'Basic Query', type: 1 }
    // ]

    for (const { label } of promptTypes) {
      test(`カテゴリ: ${label} - 各種操作`, async ({ page }) => {
        // カテゴリを選択
        const heading = page.getByRole('heading', { name: label, exact: true })
        await expect(heading).toBeVisible()
        await heading.click() 

        // No.2: レイアウト表示
        await expect(page.getByText(`プロンプト設定: ${label}`)).toBeVisible()

        // No.3: 戻るボタン
        await page.getByRole('button', { name: '戻る' }).click()
        await expect(page.getByText('プロンプト管理').nth(1)).toBeVisible()
        await heading.click() 

        // No.4: キャンセルボタン
        await page.getByRole('button', { name: 'キャンセル' }).click()
        await expect(page.getByText('プロンプト管理').nth(1)).toBeVisible()
        await heading.click() 

        // No.5: 必須変数追加
        await page.getByRole('button', { name: '必須変数を追加' }).click()
        const lastRequiredInput = page.getByPlaceholder('変数名').last()
        await lastRequiredInput.fill('test_var')
        // フォーカスアウト（blur）を意図的に実行
        await lastRequiredInput.press('Tab')

        // 値が反映されたか確認
        await expect(page.locator('input[value="test_var"]')).toBeVisible()

        // No.6: 必須変数削除（最後の trash アイコン付きボタン）
        const allDeleteButtons = page.locator('button:has(span.i-heroicons\\:trash)')
        const deleteCount = await allDeleteButtons.count()
        expect(deleteCount).toBeGreaterThan(0)
        await allDeleteButtons.nth(deleteCount - 1).click()
        // 入力値が削除されたことを確認
        await expect(page.locator('input[value="test_var"]')).toHaveCount(0)

        // No.7: オプション変数追加
        await page.getByRole('button', { name: 'オプション変数を追加' }).click()
        const lastOptionalInput = page.getByPlaceholder('変数名').last()
        await lastOptionalInput.fill('opt_var')
        // フォーカスアウト（blur）を意図的に実行
        await lastRequiredInput.press('Tab')
        // 値が反映されたか確認
        await expect(page.locator('input[value="opt_var"]')).toBeVisible()

        // No.8: オプション変数削除
        const customDeleteButtons = page.locator('button:has(span.i-heroicons\\:trash)')
        const countCustom = await customDeleteButtons.count()
        expect(countCustom).toBeGreaterThan(0)
        // 最後の削除ボタンをクリック
        await customDeleteButtons.nth(countCustom - 1).click()
        // test_var が消えたことを確認
        await expect(page.locator('input[value="opt_var"]')).toHaveCount(0)

        // No.9: 正常保存（CodeMirrorエディタに直接入力）
        const systemMessageEditor = page.locator('[aria-placeholder="システムメッセージを入力してください"]')
        await systemMessageEditor.click()
        await systemMessageEditor.fill('テスト用システムメッセージ')
        const templateEditor = page.locator('[aria-placeholder*="テンプレートを入力してください"]')
        await templateEditor.click()
        await templateEditor.fill('## 命令\n質問に対して、適切な返答を生成してください。')
        await page.getByRole('button', { name: '保存' }).click()
        await expect(page.getByText('プロンプトを保存しました')).toBeVisible()

        // No.10: ✖️ボタンでポップアップ閉じる
        await page.getByRole('button', { name: '削除' }).click()
        await page.locator('div[role="dialog"] button[aria-label="Close"]').click()
        await expect(page.locator('div[role="dialog"]')).toHaveCount(0)

        // No.11: キャンセルボタンでポップアップ閉じる
        await page.getByRole('button', { name: '削除' }).click()
        await page.locator('div[role="dialog"]').getByRole('button', { name: 'キャンセル' }).click()
        await expect(page.locator('div[role="dialog"]')).toHaveCount(0)

        // No.12: 正常削除
        await page.getByRole('button', { name: '削除' }).click()
        await page.locator('div[role="dialog"]').getByRole('button', { name: '削除' }).click()
        await expect(page.getByText('プロンプトを削除しました。デフォルトのプロンプトが使用されます。')).toBeVisible()
      })
    }
  })
})
