import { test, expect } from '@playwright/test'
import {
    commonLogin,
    navigateAndWait,
    assertSortIcon,
    clickRefreshButton,
    changePageSize,
    moveToNextAndPreviousPage,
    moveToSpecificPage,
} from '../utils/helpers'

test.describe('API操作ログ画面', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=API操作ログ'))
  })

  test('レイアウトが崩れず正しく表示されること', async ({ page }) => {
    await expect(page.getByRole('heading', { name: /API操作ログ/ })).toBeVisible()
    await expect(page.getByRole('button', { name: '全てのログを出力' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'ページ内のログを出力' })).toBeVisible()
    await expect(page.getByRole('button', { name: '更新' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '日時' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'ユーザ名' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'ユーザID' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'アクション' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'テーブル名' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '環境ID' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '詳細' })).toBeVisible()
  })

  test('詳細ポップアップが表示されること', async ({ page }) => {
    await page.getByRole('button', { name: '詳細' }).first().click()
    await expect(page.getByText('API操作ログ詳細')).toBeVisible()
  })

  test('✖️ボタンでポップアップを閉じられること', async ({ page }) => {
    await page.getByRole('button', { name: '詳細' }).first().click()
  
    const closeButton = page.locator('div[role="dialog"] button:has(span.i-heroicons\\:x-mark-20-solid)')
    await expect(closeButton).toBeVisible()
    await closeButton.click()
  
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('リフレッシュボタンで一覧が更新されること', async ({ page }) => {
    const refreshButton = page.locator('button:has(span.i-mdi\\:refresh)')
    await expect(refreshButton).toBeVisible()
    await refreshButton.click()
  
    await expect(page.getByRole('heading', { name: /API操作ログ/ })).toBeVisible()
  })

  test('表示件数を変更できること', async ({ page }) => {
    await changePageSize(page, '20')
  })

  test('ページ送りと戻る操作ができること', async ({ page }) => {
    await moveToNextAndPreviousPage(page, '20')
  })

  test('指定ページ（2ページ目）に移動できること', async ({ page }) => {
    await moveToSpecificPage(page, '20', '2')
  })

  const sortColumns = [
    { name: '日時', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
    { name: 'ユーザ名', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
    { name: 'ユーザID', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
    { name: 'アクション', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
    { name: 'テーブル名', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
    { name: '環境ID', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' }
  ]

  for (const col of sortColumns) {
    test(`${col.name}を昇順ソートできること`, async ({ page }) => {
      await assertSortIcon(page, col.name, col.asc, 1)
    })

    test(`${col.name}を降順ソートできること`, async ({ page }) => {
      await assertSortIcon(page, col.name, col.desc, 2)
    })
  }

  test('複数選択 → 選択したCSV出力ボタンが動作すること', async ({ page }) => {
    const firstRow = page.locator('tbody tr').first()
    await firstRow.locator('input[type="checkbox"]').check()
    const [download] = await Promise.all([
      page.waitForEvent('download'),
      page.getByRole('button', { name: '選択したログをCSV出力' }).click()
    ])
    expect(download.suggestedFilename()).toMatch(/\.csv$/)
  })

  test('ページ内のログを出力ボタンが動作すること', async ({ page }) => {
    const [download] = await Promise.all([
      page.waitForEvent('download'),
      page.getByRole('button', { name: 'ページ内のログを出力' }).click()
    ])
    expect(download.suggestedFilename()).toMatch(/\.csv$/)
  })

  test('全てのログを出力ボタンが動作すること', async ({ page }) => {
    const [download] = await Promise.all([
      page.waitForEvent('download'),
      page.getByRole('button', { name: '全てのログを出力' }).click()
    ])
    expect(download.suggestedFilename()).toMatch(/\.csv$/)
  })
})