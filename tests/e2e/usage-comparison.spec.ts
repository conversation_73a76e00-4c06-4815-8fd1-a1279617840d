import { test, expect } from '@playwright/test'
import {
  commonLogin,
  navigateAndWait
} from '../utils/helpers'

test.describe.serial('PNL管理者ユーザ管理機能テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=使用量'))
  })

  test('レイアウト表示', async ({ page }) => {
    await expect(page.getByText('使用量比較')).toBeVisible()
    await expect(page.getByRole('button', { name: 'CSV出力' })).toBeVisible()
    await expect(page.getByRole('button', { name: '全削除' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'リセット' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'テナントを追加' })).toBeVisible()
    await expect(page.getByText('比較サマリー')).toBeVisible()
  })

  test('テナント1件を追加', async ({ page }) => {
    await page.getByRole('button', { name: 'テナントを選択してください' }).first().click()
    await page.getByRole('option', { name: '宮若市', exact: true }).click()
  
    await page.getByRole('button', { name: 'テナントを追加' }).click()
  
    const tenantList = page.locator('div:has(h4:text("比較中のテナント"))')
    await expect(tenantList.locator('div.font-medium', { hasText: '宮若市' })).toBeVisible()
  })

  test('複数テナントを追加して、CSV出力ボタン', async ({ page }) => {
    for (const label of ['宮若市', 'PNLプレイグラウンド', '宮若市の議事録']) {
      await page.getByRole('button', { name: 'テナントを選択してください' }).first().click()
      await page.getByRole('option', { name: label, exact: true }).click()
      await page.getByRole('button', { name: 'テナントを追加' }).click()
      const tenantList = page.locator('div:has(h4:text("比較中のテナント"))')
      await expect(tenantList.locator('div.font-medium', { hasText: label })).toBeVisible()
    }

    const downloadPromise = page.waitForEvent('download')
    await page.getByRole('button', { name: 'CSV出力' }).click()
    const download = await downloadPromise
    expect(download.suggestedFilename()).toMatch(/\.csv$/)
  })

  test('全削除ボタン押下', async ({ page }) => {
    await page.getByRole('button', { name: 'テナントを選択してください' }).first().click()
    await page.getByRole('option', { name: '宮若市', exact: true }).click()
    await page.getByRole('button', { name: 'テナントを追加' }).click()
    const tenantList = page.locator('div:has(h4:text("比較中のテナント"))')
    await expect(tenantList.locator('div.font-medium', { hasText: '宮若市' })).toBeVisible()

    await page.getByRole('button', { name: '全削除' }).click()
    await expect(tenantList.locator('div.font-medium', { hasText: '宮若市' })).toHaveCount(0)
  })

  test('❌ボタンによる個別削除', async ({ page }) => {
    await page.getByRole('button', { name: 'テナントを選択してください' }).first().click()
    await page.getByRole('option', { name: '宮若市', exact: true }).click()
    await page.getByRole('button', { name: 'テナントを追加' }).click()
  
    const tenantTitle = page.getByRole('heading', { name: '比較中のテナント (1件)' })
    await expect(tenantTitle).toBeVisible()
 
    const miyawakaEntry = page.locator('div.bg-gray-50:has-text("宮若市")')
    await expect(miyawakaEntry).toBeVisible()

    const deleteButton = miyawakaEntry.locator('button:has(span.i-heroicons\\:x-mark)')
    await expect(deleteButton).toBeVisible()
    await deleteButton.click()
 
    await expect(miyawakaEntry).toHaveCount(0)
  })  

  test('削除ボタンで個別削除', async ({ page }) => {
    await page.getByRole('button', { name: 'テナントを選択してください' }).first().click()
    await page.getByRole('option', { name: '宮若市', exact: true }).click()
    await page.getByRole('button', { name: 'テナントを追加' }).click()
  
    const tenantList = page.locator('div:has(h4:text("比較中のテナント"))')
    await expect(tenantList.locator('div.font-medium', { hasText: '宮若市' })).toBeVisible()
  
    const miyawakaBlock = page.locator('div.bg-gray-50:has-text("宮若市")')
    const deleteButton = miyawakaBlock.locator('button:has(span.i-heroicons\\:x-mark)')
  
    await expect(deleteButton).toBeVisible()
    await deleteButton.click()
  
    await expect(tenantList.locator('div.font-medium', { hasText: '宮若市' })).toHaveCount(0)
  })

  test('リセットボタン押下で項目初期化', async ({ page }) => {
    await page.getByRole('button', { name: 'テナントを選択してください' }).first().click()
    await page.getByRole('option', { name: '宮若市', exact: true }).click()

    await page.locator('input[placeholder="環境IDを入力"]').fill('env123')
    await page.locator('input[placeholder="リクエストIDを入力"]').fill('req456')
    await page.locator('input[placeholder="セッションIDを入力"]').fill('sess789')
    await page.locator('input[type="date"]').nth(0).fill('2025-06-24')
    await page.locator('input[type="date"]').nth(1).fill('2025-07-24')
  
    await page.getByRole('button', { name: '環境タイプを選択' }).first().click()
    await page.getByRole('option').nth(1).click()
  
    await page.getByRole('button', { name: 'リセット' }).click()

    // await expect(page.getByRole('button', { name: 'テナントを選択してください' })).toBeVisible()
    await expect(page.locator('input[placeholder="環境IDを入力"]')).toHaveValue('')
    await expect(page.locator('input[placeholder="リクエストIDを入力"]')).toHaveValue('')
    await expect(page.locator('input[placeholder="セッションIDを入力"]')).toHaveValue('')
    await expect(page.locator('input[type="date"]').nth(0)).toHaveValue('')
    await expect(page.locator('input[type="date"]').nth(1)).toHaveValue('')
    const envTypeButton = page.locator('label:text("環境タイプ")').locator('..').locator('button')
    await expect(envTypeButton).toHaveText('環境タイプを選択')
  })
})
