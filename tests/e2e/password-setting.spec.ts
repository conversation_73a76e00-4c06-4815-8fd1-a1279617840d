import { test, expect } from '@playwright/test'
import { navigateAndWait } from '../utils/helpers'

test.describe('パスワード設定画面のテスト', () => {

  // let username: string
  let username: string = 'auto-user-1315'
  const email = '<EMAIL>'
  const password = 'Pnl2025!' // 仮パスワード
  const tenantId = 'pnl_playground'

  test.beforeEach(async ({ page }) => {
    if (!username) {
      // 初回だけユーザ作成const password = 'Pnl2025!'
      await page.goto('https://chat.dev.pnl-ai-concierge.com/auth/login')
      await page.getByPlaceholder('Ex: playnextlab').fill(tenantId)

      // await navigateAndWait(page, () => page.goto('/login'))
      await page.getByPlaceholder('Ex: john.doe').fill('new-user-test2')
      await page.getByPlaceholder('パスワードを入力してください').fill(password)
      await page.getByRole('button', { name: 'ログイン' }).click()
      await page.waitForTimeout(5000)

      // await page.locator('button:has-text("宮若市")').first().click()
    
      // await page.locator('span', { hasText: 'PNLプレイグラウンド' }).click()
      // await page.waitForTimeout(5000)

      const envButton = page.locator('button:has-text("本番環境")')
      await envButton.click()
      await page.locator('span', { hasText: '検証' }).click()
      await page.waitForTimeout(2000)

      await navigateAndWait(page, () => page.click('text=設定'))
      await navigateAndWait(page, () => page.click('text=ユーザ管理'))

      await page.waitForSelector('button:has-text("ユーザ追加"), button:has-text("ユーザを追加")')
      const normalAddButton = page.locator('button', { hasText: 'ユーザ追加' })
      const emptyAddButton = page.locator('button', { hasText: 'ユーザを追加' })

      if (await normalAddButton.isVisible()) {
        await normalAddButton.click()
      } else if (await emptyAddButton.isVisible()) {
        await emptyAddButton.click()
      } else {
        throw new Error('ユーザ追加ボタンが見つかりませんでした！')
      }

      const random = Math.floor(Math.random() * 10000)
      username = `auto-user-${random}`

      await page.getByPlaceholder('例: <EMAIL>').fill(email)
      await page.getByPlaceholder('ログインのユーザ名').fill(username)
      await page.getByPlaceholder('ユーザの仮パスワード').fill(password)
      await page.getByPlaceholder('ユーザの名前').fill(username)

      await page.getByRole('button', { name: '確定' }).click()
      await page.waitForTimeout(3000)

      await page.locator('#headlessui-menu-button-v-2-3').getByRole('button', { name: 'pnl_win_thandarlwin' }).click()
      await page.getByRole('menuitem', { name: 'ログアウト' }).click()
    }

    // 新しいユーザでログイン
    await page.goto('/login')
    await page.getByPlaceholder('Ex: playnextlab').fill(tenantId)
    await page.getByPlaceholder('Ex: john.doe').fill(username)
    await page.getByPlaceholder('パスワードを入力してください').fill(password)
    await page.getByRole('button', { name: 'ログイン' }).click()
  })

  test('パスワード設定画面のレイアウト表示', async ({ page }) => {
    await expect(page.getByText('パスワードの設定')).toBeVisible()
    await expect(page.getByPlaceholder('新しいパスワードを入力してください')).toBeVisible()
    await expect(page.getByPlaceholder('新しいパスワードを再度入力してください')).toBeVisible()
    await expect(page.getByRole('button', { name: 'パスワードを設定' })).toBeVisible()
  })

  test('パスワード入力欄の目ボタンで表示・非表示を切り替えできる', async ({ page }) => {
    const passwordInput = page.getByPlaceholder('新しいパスワードを入力してください')
    await passwordInput.fill('TestPassword123!')

    // パスワード入力欄に対応する目ボタン（1個目）
    const passwordEyeButton = page.locator('button').nth(0) // 1番目のボタン
    await passwordEyeButton.waitFor()

    // (eye) をクリックして表示状態にする
    await passwordEyeButton.click()
    await expect(passwordInput).toHaveAttribute('type', 'text')

    // (eye-slash) に切り替わったはずなので、再びクリックして非表示に戻す
    await passwordEyeButton.click()
    await expect(passwordInput).toHaveAttribute('type', 'password')
  })

  test('パスワード確認欄の目ボタンで表示・非表示を切り替えできる', async ({ page }) => {
    const confirmPasswordInput = page.getByPlaceholder('新しいパスワードを再度入力してください')
    await confirmPasswordInput.fill('TestPassword123!')

    // パスワード確認欄に対応する目ボタン（2個目）
    const confirmEyeButton = page.locator('button').nth(1) // 2番目のボタン
    await confirmEyeButton.waitFor()

    // (eye) をクリックして表示状態にする
    await confirmEyeButton.click()
    await expect(confirmPasswordInput).toHaveAttribute('type', 'text')

    // (eye-slash) に切り替わったはずなので、再びクリックして非表示に戻す
    await confirmEyeButton.click()
    await expect(confirmPasswordInput).toHaveAttribute('type', 'password')
  })

  test('必須項目未入力時のエラーメッセージ表示', async ({ page }) => {
    await page.getByRole('button', { name: 'パスワードを設定' }).click()
    await expect(page.getByText('パスワードは必須です')).toBeVisible()
    await expect(page.getByText('パスワードの確認は必須です')).toBeVisible()
  })

  test('パスワード不一致時のエラーメッセージ表示', async ({ page }) => {
    await page.getByPlaceholder('新しいパスワードを入力してください').fill('TestPassword123!')
    await page.getByPlaceholder('新しいパスワードを再度入力してください').fill('DifferentPassword!')
    await page.getByRole('button', { name: 'パスワードを設定' }).click()
    await expect(page.getByText('パスワードが一致しません')).toBeVisible()
  })

  test('正常パスワード再設定', async ({ page }) => {
    const newPassword = 'Pnl2024!'
    await page.getByPlaceholder('新しいパスワードを入力してください').fill(newPassword)
    await page.getByPlaceholder('新しいパスワードを再度入力してください').fill(newPassword)
    await page.getByRole('button', { name: 'パスワードを設定' }).click()

    // ダッシュボードに遷移するか確認
    await expect(page.locator('h1 span:has-text("ダッシュボード")')).toBeVisible()
  })
})