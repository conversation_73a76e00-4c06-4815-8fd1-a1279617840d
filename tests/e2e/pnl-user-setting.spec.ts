import { test, expect } from '@playwright/test'
import {
  commonLogin,
  navigateAndWait,
  openConfirmPopup,
  assertSortIcon,
  clickRefreshButton,
  changePageSize,
  moveToNextAndPreviousPage,
  moveToSpecificPage
} from '../utils/helpers'

let newUserName: string
let newUserMail: string
// const newUserName = 'pnl_win_thandarlwin'
// const newUserMail = '<EMAIL>'
const password = 'Pnl2024!'
const emailDomain = 'playnext-lab.co.jp'

test.describe.serial('PNL管理者ユーザ管理機能テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=PNL管理者ユーザ'))
  })

  test('レイアウト表示', async ({ page }) => {
    await expect(page.getByText('PNL管理者ユーザ一覧')).toBeVisible()
    await expect(page.getByRole('button', { name: 'PNL管理者ユーザ追加' })).toBeVisible()
    await expect(page.getByPlaceholder('ユーザ名で検索...')).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '名前' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'アカウント情報' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '権限' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '作成日時' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '操作' })).toBeVisible()
  })

  test.describe('PNL管理者ユーザ追加機能', () => {
    test('追加ポップアップ表示確認', async ({ page }) => {
      await page.getByRole('button', { name: 'PNL管理者ユーザ追加' }).click()
      await expect(page.getByText('新規PNL管理者ユーザ登録')).toBeVisible()
      await expect(page.getByPlaceholder('例: <EMAIL>')).toBeVisible()
      await expect(page.getByPlaceholder('ログインのユーザ名')).toBeVisible()
      await expect(page.getByPlaceholder('ユーザの仮パスワード')).toBeVisible()
      await expect(page.getByPlaceholder('ユーザの名前')).toBeVisible()
      await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('仮パスワード目ボタン操作（表示→非表示）', async ({ page }) => {
        await page.getByRole('button', { name: 'PNL管理者ユーザ追加' }).click()
      
        const pwInput = page.getByPlaceholder('ユーザの仮パスワード')
        await pwInput.fill('Test12345!')
        await expect(pwInput).toHaveAttribute('type', 'text')
      
        // 目ボタンを押してマスクする
        await page.locator('span[class*="i-heroicons\\:eye-slash-20-solid"]').click()
        await expect(pwInput).toHaveAttribute('type', 'password')
      
        // 再度押して表示に戻す
        await page.locator('span[class*="i-heroicons\\:eye-20-solid"]').click()
        await expect(pwInput).toHaveAttribute('type', 'text')
    })

    test('仮パスワード生成確認', async ({ page }) => {
      await page.getByRole('button', { name: 'PNL管理者ユーザ追加' }).click()
      const pwInput = page.getByPlaceholder('ユーザの仮パスワード')
      await page.locator('div.cursor-pointer', { hasText: '仮パスワードを生成する' }).dispatchEvent('click')
      await expect(pwInput).not.toHaveValue('', { timeout: 3000 })
    })

    test('必須項目未入力時のエラーメッセージ', async ({ page }) => {
      await page.getByRole('button', { name: 'PNL管理者ユーザ追加' }).click()
      await page.getByRole('button', { name: '確定' }).click()
      await expect(page.getByText('有効なメールアドレスを入力してください')).toBeVisible()
      await expect(page.getByText('ユーザ名は必須です')).toBeVisible()
      await expect(page.getByText('パスワードは6文字以上で入力してください')).toBeVisible()
    })

    test('正常にPNL管理者ユーザを追加できること', async ({ page }) => {
      const now = new Date()
      const hhmmss = now.toTimeString().slice(0, 8).replace(/:/g, '')
      newUserName = `pnl_admin_${hhmmss}`
      newUserMail = `pnl_admin+${hhmmss}@${emailDomain}`

      await page.getByRole('button', { name: 'PNL管理者ユーザ追加' }).click()
      await page.getByPlaceholder('例: <EMAIL>').fill(newUserMail)
      await page.getByPlaceholder('ログインのユーザ名').fill(newUserName)
      await page.getByPlaceholder('ユーザの仮パスワード').fill(password)
      await page.getByPlaceholder('ユーザの名前').fill(`テスト管理者 ${hhmmss}`)
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText(newUserName)).toBeVisible({ timeout: 30000 })
    })

    test('✖️ボタンでポップアップを閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: 'PNL管理者ユーザ追加' }).click()
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('キャンセルボタンでポップアップを閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: 'PNL管理者ユーザ追加' }).click()
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })
  })

  test.describe('PNL管理者ユーザ検索・表示・ソート機能', () => {
    test('存在しないキーワードで検索すると「データがありません」表示', async ({ page }) => {
      await page.getByPlaceholder('ユーザ名で検索...').fill('nonexistent_user_xyz')
      await expect(page.getByText('データがありません')).toBeVisible({ timeout: 5000 })
    })

    test('部分一致検索できること', async ({ page }) => {
      const keyword = newUserName?.slice(0, 8) || 'pnl_admin'
      await page.getByPlaceholder('ユーザ名で検索...').fill(keyword)
      await expect(page.getByText(newUserName)).toBeVisible({ timeout: 5000 })
    })

    test('リフレッシュボタンで一覧が更新されること', async ({ page }) => {
      await clickRefreshButton(page)
      await expect(page.getByText('PNL管理者ユーザ一覧')).toBeVisible()
    })

    test('表示件数を変更できること', async ({ page }) => {
      await changePageSize(page, '20')
    })

    test('ページ送りと戻る操作ができること', async ({ page }) => {
      await moveToNextAndPreviousPage(page, '10')
    })

    // ロボットチャットアイコンが2ページに一致してるので、自動押せないため、一応コメントアウトしてた
    test('指定ページ（2ページ目）に移動できること', async ({ page }) => {
      await moveToSpecificPage(page, '10', '2')
    })

    const sortColumns = [
      { name: '名前', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: 'アカウント情報', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: '権限', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: '作成日時', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' }
    ]

    for (const col of sortColumns) {
      test(`${col.name}を昇順ソートできること`, async ({ page }) => {
        await assertSortIcon(page, col.name, col.asc, 1)
      })

      test(`${col.name}を降順ソートできること`, async ({ page }) => {
        await assertSortIcon(page, col.name, col.desc, 2)
      })
    }
  })

  test.describe('PNL管理者ユーザ編集機能', () => {
    test('編集ポップアップ表示確認', async ({ page }) => {
      await openConfirmPopup(page, newUserName, 'PNL管理者ユーザ編集')
      await expect(page.getByText('PNL管理者ユーザを編集')).toBeVisible()
      await expect(page.getByPlaceholder('例: <EMAIL>')).toHaveValue(newUserMail)
      await expect(page.getByPlaceholder('ログインのユーザ名')).toHaveValue(newUserName)
    })

    test('必須項目未入力時のエラーメッセージ表示', async ({ page }) => {
      await openConfirmPopup(page, newUserName, 'PNL管理者ユーザ編集')
      await page.getByPlaceholder('例: <EMAIL>').fill('')
      await page.getByRole('button', { name: '確定' }).click()
      await expect(page.getByText('有効なメールアドレスを入力してください')).toBeVisible()
    })

    test('✖️ボタンでポップアップを閉じられること', async ({ page }) => {
        await openConfirmPopup(page, newUserName, 'PNL管理者ユーザ編集')
        await page.locator('button[aria-label="Close"]').click()
        await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('キャンセルボタンでポップアップを閉じられること', async ({ page }) => {
        await openConfirmPopup(page, newUserName, 'PNL管理者ユーザ編集')
        await page.getByRole('button', { name: 'キャンセル' }).click()
        await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('正常に編集できること', async ({ page }) => {
      await openConfirmPopup(page, newUserName, 'PNL管理者ユーザ編集')
      const newLabel = `編集済み${newUserName}`
      await page.getByPlaceholder('ユーザの名前').fill(newLabel)
      await page.getByRole('button', { name: '確定' }).click()
      await expect(page.getByText(newLabel)).toBeVisible()
    })
  })

  test.describe('PNL管理者ユーザ削除機能', () => {
    test('削除ポップアップ表示確認', async ({ page }) => {
      await openConfirmPopup(page, newUserName, 'PNL管理者ユーザ削除')
      await expect(page.getByText('PNL管理者ユーザ削除の確認')).toBeVisible()
      await expect(page.getByText(`ユーザ「${newUserName}」を削除しますか？`)).toBeVisible()
    })

    test('✖️ボタンでポップアップを閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newUserName, 'PNL管理者ユーザ削除')
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('キャンセルボタンでポップアップを閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newUserName, 'PNL管理者ユーザ削除')
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('正常に削除できること', async ({ page }) => {
      await openConfirmPopup(page, newUserName, 'PNL管理者ユーザ削除')
      await page.getByRole('button', { name: '削除' }).click()
      await expect(page.getByText(newUserName)).toHaveCount(0, { timeout: 5000 })
    })
  })
})