import { test, expect } from '@playwright/test'
import {
  askChatBotMobile
} from '../utils/helpers'

// const CHATBOT_URL = 'https://chat.dev.pnl-ai-concierge.com/chatbot?tenantId=miyawaka'
const CHATBOT_URL = 'https://chat.dev.pnl-ai-concierge.com/chatbot?tenantId=pnl_playground'

test.describe('宮若市AIコンシェルジュ - チャットボット画面', () => {

  test.beforeEach(async ({ page }) => {
    await page.goto(CHATBOT_URL)
    await page.waitForTimeout(10000)
    // チャットボットアイコン（猫アイコン）をクリックして開く
    // await page.locator('.avatar-container').click()
    await page.locator('.chat-bubble-container').click()
  })

  test('レイアウト表示', async ({ page }) => {
    await expect(page.getByText('こんにちは！宮若市AIコンシェルジュです！')).toBeVisible({ timeout: 15000 })
  })

  test('メッセージ送信 → 回答とアンケート表示 → アンケートの「はい」ボタンを押せる', async ({ page }) => {
    // 利用者用チャットボットを開いて、確認メッセージを送信
    await askChatBotMobile(page)

    // アンケート表示確認
    await expect(page.getByText('お役に立てましたか？')).toBeVisible({ timeout: 8000 })
  
    // "はい" と "いいえ" の div ボタン確認（順不同で出る可能性もあるので `hasText` で確認）
    const surveyButtons = page.locator('div.aiko-cursor-pointer')
    await expect(surveyButtons.filter({ hasText: 'はい' })).toBeVisible()
    await expect(surveyButtons.filter({ hasText: 'いいえ' })).toBeVisible()

    // 「はい」ボタンをクリック（divタグ）
    await surveyButtons.filter({ hasText: 'はい' }).click()
    // 次の質問表示を確認
    await expect(page.getByText('他にもお困りのことはありますか？')).toBeVisible({ timeout: 8000 })
  })

  test('閉じるボタンでチャットを閉じる', async ({ page }) => {
    const openChatWindow = page.locator('.aiko-chat-window').filter({
      has: page.getByText('PNLチャットボット') // 宮若市AIコンシェルジュ
    })
    await expect(openChatWindow).toBeVisible({ timeout: 10000 })
  
    const closeIcon = openChatWindow.locator('.aiko-w-20 svg').first()
    await page.waitForTimeout(300)
    await closeIcon.click({ force: true })
  
    await expect(page.getByText('こんにちは！宮若市AIコンシェルジュです！')).not.toBeVisible({ timeout: 5000 })
  })

  test('Refresh → キャンセルで戻る → チャットをクリアする', async ({ page }) => {
    await askChatBotMobile(page)
  
    const openChatWindow = page.locator('.aiko-chat-window').filter({
      has: page.getByText('PNLチャットボット') // 宮若市AIコンシェルジュ
    })
    await expect(openChatWindow).toBeVisible({ timeout: 5000 })
  
    const refreshIcon = openChatWindow.locator('.aiko-w-20 svg[title="Reset chat"]').first()
    await refreshIcon.click()
  
    await page.getByRole('button', { name: 'キャンセル' }).click()
    await expect(page.getByText('こんにちは！宮若市AIコンシェルジュです！')).toBeVisible({ timeout: 8000 })
  
    await refreshIcon.click()
    await page.getByRole('button', { name: 'チャットをクリアする' }).click()
    await expect(page.getByText('こんにちは！宮若市AIコンシェルジュです！')).toBeVisible({ timeout: 8000 })
    await expect(page.locator('text=お役に立てましたか？')).not.toBeVisible()
  })
})
