import { test, expect } from '@playwright/test'
import {
  navigateAndWait,
  commonLogin,
  openConfirmPopup,
  assertSortIcon,
  clickRefreshButton,
  changePageSize,
  moveToNextAndPreviousPage,
  moveToSpecificPage
} from '../utils/helpers'

let newAccountName: string
let newAccountMail: string
let newUserName: string

// const newAccountName = 'auto_user_184420'
// const newAccountMail = '<EMAIL>'
// const newUserName = 'auto_name_184420'

const password = 'Pnl2024!'
const emailDomain = 'playnext-lab.co.jp'

test.describe.serial('ユーザ管理機能テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)

    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=ユーザ管理'))
  })

  test.describe('表示機能', () => {
    test('レイアウト表示', async ({ page }) => {
      await expect(page.getByText('ユーザ一覧')).toBeVisible()
      await expect(page.getByRole('button', { name: 'ユーザ追加' })).toBeVisible()
      await expect(page.getByPlaceholder('ユーザ名の検索')).toBeVisible()
      await expect(page.locator('div[role="button"]', { hasText: '権限' }).first()).toBeVisible()
      await expect(page.locator('div[role="button"]', { hasText: 'ステータス' }).first()).toBeVisible()
      await expect(page.getByRole('columnheader', { name: '名前' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: 'アカウント情報' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: '権限' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: 'ステータス' })).toBeVisible()
    })
  })  
  
  test.describe('ソート機能の確認', () => {
    const columns = [
      { name: '名前', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: 'アカウント情報', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' },
      { name: '権限', asc: 'bars-arrow-up-20-solid', desc: 'bars-arrow-down-20-solid' }
    ]

    for (const col of columns) {
      test(`${col.name} を昇順ソートできること`, async ({ page }) => {
        await assertSortIcon(page, col.name, col.asc, 1)
      })

      test(`${col.name} を降順ソートできること`, async ({ page }) => {
        await assertSortIcon(page, col.name, col.desc, 2)
      })
    }
  })

  test.describe('ユーザ追加機能', () => {
    test('ユーザ追加ポップアップ表示確認', async ({ page }) => {
      await page.getByRole('button', { name: 'ユーザ追加' }).click()
      await expect(page.getByText('新規ユーザ登録')).toBeVisible()
      await expect(page.getByText('新しいユーザを作成します。')).toBeVisible()
      await expect(page.getByPlaceholder('例: <EMAIL>')).toBeVisible()
      await expect(page.getByPlaceholder('ログインのユーザ名')).toBeVisible()
      await expect(page.getByPlaceholder('ユーザの仮パスワード')).toBeVisible()
      await expect(page.getByPlaceholder('ユーザの名前')).toBeVisible()
      await expect(page.locator('button:has-text("スタッフ")')).toBeVisible()

      await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('目ボタン押下（表示・非表示）', async ({ page }) => {
      await page.getByRole('button', { name: 'ユーザ追加' }).click()

      const passwordInput = page.getByPlaceholder('ユーザの仮パスワード')

      // 初期状態は text type になってる
      await passwordInput.fill('TestPassword123!')
      await expect(passwordInput).toHaveAttribute('type', 'text')

      // 目ボタンを押して type が password になるか確認
      await page.locator('button span.i-heroicons\\:eye-slash-20-solid').click()
      await expect(passwordInput).toHaveAttribute('type', 'password')

      // もう1回押して type が text に戻るか確認
      await page.locator('button span.i-heroicons\\:eye-20-solid').click()
      await expect(passwordInput).toHaveAttribute('type', 'text')
    })

    test('仮パスワードの生成確認', async ({ page }) => {
      await page.getByRole('button', { name: 'ユーザ追加' }).click()

      const passwordField = page.getByPlaceholder('ユーザの仮パスワード')
      const generateButton = page.locator('div.cursor-pointer', { hasText: '仮パスワードを生成する' })

      // 普通の click ではなく dispatchEvent で強制的に発火
      await generateButton.dispatchEvent('click')

      // パスワードが空じゃなくなるのを待つ
      await expect(passwordField).not.toHaveValue('', { timeout: 3000 })
    })

    test('ユーザ追加ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: 'ユーザ追加' }).click()
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ユーザ追加ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await page.getByRole('button', { name: 'ユーザ追加' }).click()
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('必須項目未入力時のエラーメッセージ表示', async ({ page }) => {
      await page.getByRole('button', { name: 'ユーザ追加' }).click()
      await page.getByRole('button', { name: '確定' }).click()
      await expect(page.getByText('無効なメールアドレスです。')).toBeVisible()
      await expect(page.getByText('ユーザ名は3文字以上で入力してください。')).toBeVisible()
      await expect(page.getByText('パスワードは必須です。')).toBeVisible()
    })

    test('正常にユーザ追加できること', async ({ page }) => {
      const now = new Date()
      const hhmmss = now.toTimeString().slice(0, 8).replace(/:/g, '') // "141503" みたいな文字列
      newAccountName = `auto_user_${hhmmss}`
      newAccountMail = `win.thandarlwin+${hhmmss}@${emailDomain}`
      newUserName = `auto_name_${hhmmss}`

      await page.getByRole('button', { name: 'ユーザ追加' }).click()
      await page.getByPlaceholder('例: <EMAIL>').fill(newAccountMail)
      await page.getByPlaceholder('ログインのユーザ名').fill(newAccountName)
      await page.getByPlaceholder('ユーザの仮パスワード').fill(password)
      await page.getByPlaceholder('ユーザの名前').fill(newUserName)
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText(newAccountName)).toBeVisible({ timeout: 50000 })
    })
  })

  test.describe('ユーザ検索機能テスト', () => {
    test('存在しないキーワードで検索するとデータなし表示', async ({ page }) => {
      await page.getByPlaceholder('ユーザ名の検索').fill('存在しないユーザXYZ')
      await expect(page.getByText('データがありません')).toBeVisible({ timeout: 30000 })
    })

    test('存在するユーザ名で部分一致検索できること', async ({ page }) => {
      await page.getByPlaceholder('ユーザ名の検索').fill(newAccountName)
      await expect(page.getByText(newAccountName)).toBeVisible({ timeout: 30000 })
    })

    const roles = ['スタッフ', '管理者', 'PNL管理者']
    for (const role of roles) {
      test(`${role} で絞り込んで検索できること`, async ({ page }) => {
        // 権限ドロップダウンを開く
        await page.locator('div[role="button"]', { hasText: '権限' }).first().click()

        // 正確にオプション選択（完全一致で指定）
        await page.getByRole('option', { name: role, exact: true }).click()

        // 少し待ってからチェック
        await page.waitForTimeout(3000)

        // 検索結果の「データがありません」判定
        const noDataLocator = page.locator('td', { hasText: 'データがありません' })
        if (await noDataLocator.isVisible()) {
          console.warn(`⚠️ ${role} 権限に該当するデータがありません。スキップします。`)
          return
        }

        // 検索結果の行を取得
        const rows = page.locator('tbody tr')
        const rowCount = await rows.count()

        for (let i = 0; i < rowCount; i++) {
          const roleCell = rows.nth(i).locator('td').nth(3) // 正しく4列目（0始まりなのでnth(3)）
          await expect(roleCell).toHaveText(role)
        }
      })
    }

    const statuses = ['有効', '無効']
    for (const status of statuses) {
      test(`ステータスで「${status}」を絞り込んで検索できること`, async ({ page }) => {
        await page.locator('div[role="button"]', { hasText: 'ステータス' }).first().click()
        await page.getByRole('option', { name: status, exact: true }).click()
        await page.waitForTimeout(3000)

        const rows = page.locator('tbody tr')
        const rowCount = await rows.count()

        for (let i = 0; i < rowCount; i++) {
          const statusCell = rows.nth(i).locator('td').nth(4) //  正しく5列目（0始まりなのでnth(4)）
          await expect(statusCell).toHaveText(status)
        }
      })
    }

    test('リフレッシュボタンでユーザ一覧が更新されること', async ({ page }) => {
      await clickRefreshButton(page)
      await expect(page.getByText('ユーザ一覧')).toBeVisible()
    })

    test('表示件数を変更できること', async ({ page }) => {
      await changePageSize(page, '5')
    })

    test('ページ送りと戻る操作ができること', async ({ page }) => {
      await moveToNextAndPreviousPage(page, '5')
    })

    test('指定ページ（2ページ目）に移動できること', async ({ page }) => {
      await moveToSpecificPage(page, '5', '2')
    })
  })

  test.describe('ユーザ編集機能テスト', () => {
    test('ユーザ編集ポップアップが表示されること', async ({ page }) => {
      await openConfirmPopup(page, newAccountName, 'ユーザ編集')

      await expect(page.getByText('ユーザを編集')).toBeVisible()
      await expect(page.getByText('ユーザの情報を編集します。')).toBeVisible()
      await expect(page.getByPlaceholder('例: <EMAIL>')).toHaveValue(newAccountMail)
      // 非活性なので、この項目が確認できない
      await expect(page.getByPlaceholder('ログインのユーザ名')).toHaveValue(newAccountName)
      await expect(page.getByPlaceholder('ユーザの名前')).toHaveValue(newUserName)
      await expect(page.locator('button:has-text("スタッフ")')).toBeVisible()
      await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('ユーザ編集ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newAccountName, 'ユーザ編集')
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ユーザ編集ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newAccountName, 'ユーザ編集')
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('必須項目未入力時のエラーメッセージ表示', async ({ page }) => {
      await openConfirmPopup(page, newAccountName, 'ユーザ編集')

      await page.getByPlaceholder('例: <EMAIL>').fill('')
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText('無効なメールアドレスです。')).toBeVisible()
    })

    test('正常にユーザを編集できること', async ({ page }) => {
      await openConfirmPopup(page, newAccountName, 'ユーザ編集')

      const editedName = `auto-edited-${newUserName}`
      await page.getByPlaceholder('ユーザの名前').fill(editedName)
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText(editedName)).toBeVisible()
    })
  })

  test.describe('ユーザ無効化/有効化機能テスト', () => {
    test('ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newAccountName, '有効化')
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newAccountName, '有効化')
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('無効状態から有効状態へ切り替えできること', async ({ page }) => {
      const row = page.locator('tr', { hasText: newAccountName })
      await expect(row.locator('td >> text=無効')).toBeVisible()

      await openConfirmPopup(page, newAccountName, '有効化')

      await expect(page.getByText('ステータス変更')).toBeVisible()
      await expect(page.getByText('このユーザのステータスを有効に変更しますか？')).toBeVisible()
      await page.getByRole('button', { name: '変更' }).click()

      await expect(row.getByText('有効')).toBeVisible({ timeout: 5000 })
    })

    test('有効状態から無効状態へ切り替えできること', async ({ page }) => {
      const row = page.locator('tr', { hasText: newAccountName })
      await expect(row.locator('td >> text=有効')).toBeVisible()

      await openConfirmPopup(page, newAccountName, '無効化')

      await expect(page.getByText('ステータス変更')).toBeVisible()
      await expect(page.getByText('このユーザのステータスを無効に変更しますか？')).toBeVisible()
      await page.getByRole('button', { name: '変更' }).click()

      await expect(row.getByText('無効')).toBeVisible({ timeout: 5000 })
    })
  })

  test.describe('ユーザ削除機能テスト', () => {
    test('ユーザ削除ポップアップが表示されること', async ({ page }) => {
      await openConfirmPopup(page, newAccountName, 'ユーザ削除')

      await expect(page.getByText('ユーザ削除の確認')).toBeVisible()
      await expect(page.getByText(`ユーザ「${newAccountName}」を削除しますか？`)).toBeVisible()
      await expect(page.getByRole('button', { name: '削除' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('ユーザ削除ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newAccountName, 'ユーザ削除')
      await page.locator('button[aria-label="Close"]').click()

      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ユーザ削除ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newAccountName, 'ユーザ削除')
      await page.getByRole('button', { name: 'キャンセル' }).click()

      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('正常にユーザを削除できること', async ({ page }) => {
      await openConfirmPopup(page, newAccountName, 'ユーザ削除')

      await page.getByRole('button', { name: '削除' }).click()

      // 削除ボタン押したあとで、対象ユーザが消えるまで待つ
      await expect(page.getByText(newAccountName)).toHaveCount(0, { timeout: 5000 })
    })
  })
})
