import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/chatbot',
  timeout: 30 * 1000,
  retries: 0,
  use: {
    baseURL: 'https://chat.dev.pnl-ai-concierge.com',
    headless: true,
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    ignoreHTTPSErrors: true
  },
  projects: [
    {
      name: 'PC - Chrome',
      testMatch: /.*\.pc\.spec\.ts/,
      use: {
        browserName: 'chromium',
        channel: 'chrome'
      }
    },
    {
      name: 'PC - Edge',
      testMatch: /.*\.pc\.spec\.ts/,
      use: {
        browserName: 'chromium',
        channel: 'msedge'
      }
    },
    {
      name: 'PC - Safari',
      testMatch: /.*\.pc\.spec\.ts/,
      use: {
        browserName: 'webkit'
      }
    },
    {
      name: 'Mobile - Android Chrome',
      testMatch: /.*\.mobile\.spec\.ts/,
      use: {
        ...devices['Pixel 5']
      }
    },
    {
      name: 'Mobile - iPhone Safari',
      testMatch: /.*\.mobile\.spec\.ts/,
      use: {
        ...devices['iPhone 12']
      }
    }
  ]
});


