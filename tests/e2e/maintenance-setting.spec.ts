import { test, expect } from '@playwright/test'
import { format } from 'date-fns'
import { 
    commonLogin,
    navigateAndWait 
} from '../utils/helpers'

test.describe.serial('チャットボットのメンテナンス設定画面', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=メンテナンス設定'))
  })

  test('無期限メンテナンス - レイアウト表示', async ({ page }) => {
    await expect(page.getByText('無期限メンテナンス').first()).toBeVisible()
    await expect(page.getByText('稼働中')).toBeVisible()
    await expect(page.getByPlaceholder('ただいまメンテナンス中です。しばらくしてもう一度お試しください。')).toBeVisible()
    await expect(page.getByRole('button', { name: 'メンテナンス開始' })).toBeVisible()
  })  

  test('メンテナンス開始ポップアップ表示確認', async ({ page }) => {
    await page.getByRole('button', { name: 'メンテナンス開始' }).click()
    await expect(page.getByText('チャットボットのメンテナンス有効化の確認')).toBeVisible()
  })

  test('ポップアップ ✖️ ボタンで閉じる', async ({ page }) => {
    await page.getByRole('button', { name: 'メンテナンス開始' }).click()
    await page.locator('button[aria-label="Close"]').click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('ポップアップ キャンセルボタンで閉じる', async ({ page }) => {
    await page.getByRole('button', { name: 'メンテナンス開始' }).click()
    await page.getByRole('button', { name: 'キャンセル' }).click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('メンテナンス開始（有効化）', async ({ page }) => {
    const notice = `ただいまメンテナンス中です。しばらくしてもう一度お試しください。`
    await page.getByPlaceholder(notice).fill(notice)
    await page.getByRole('button', { name: 'メンテナンス開始' }).click()
    await page.getByRole('button', { name: '有効化' }).click()
    await expect(page.getByText('メンテナンス中').first()).toBeVisible() // or use locator('button >> text=...')
  })  

  test('メンテナンス終了ポップアップ表示確認', async ({ page }) => {
    await page.getByRole('button', { name: 'メンテナンス終了' }).click()
    await expect(page.getByText('チャットボットのメンテナンス無効化の確認')).toBeVisible()
  })

  test('終了ポップアップ ✖️ ボタンで閉じる', async ({ page }) => {
    await page.getByRole('button', { name: 'メンテナンス終了' }).click()
    await page.locator('button[aria-label="Close"]').click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('終了ポップアップ キャンセルボタンで閉じる', async ({ page }) => {
    await page.getByRole('button', { name: 'メンテナンス終了' }).click()
    await page.getByRole('button', { name: 'キャンセル' }).click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  })

  test('メンテナンス終了（無効化）', async ({ page }) => {
    await page.getByRole('button', { name: 'メンテナンス終了' }).click()
    await page.getByRole('button', { name: '無効化' }).click()
    await expect(page.getByRole('button', { name: 'メンテナンス開始' })).toBeVisible()
  })

  test('スケジュールメンテナンス - レイアウト表示', async ({ page }) => {
    await page.locator('[data-tour="maintenance-scheduled"]').click()
  
    await expect(page.getByText('稼働中')).toBeVisible()
    await expect(page.getByPlaceholder('ただいまメンテナンス中です。しばらくしてもう一度お試しください。')).toBeVisible()
    await expect(page.getByText('開始日時').first()).toBeVisible()
    await expect(page.getByText('終了日時').first()).toBeVisible()  
    await expect(page.getByRole('button', { name: 'メンテナンス開始' })).toBeVisible()
  })
  
  test('スケジュールメンテナンス ポップアップ表示確認', async ({ page }) => {
    await page.locator('[data-tour="maintenance-scheduled"]').click()
  
    await page.getByRole('button', { name: 'メンテナンス開始' }).click()
    await expect(page.getByText('チャットボットのメンテナンス有効化の確認')).toBeVisible()
  })  

  test('スケジュールメンテナンスポップアップ閉じる + 有効化', async ({ page }) => {
    await page.locator('[data-tour="maintenance-scheduled"]').click()
    const notice = `ただいまメンテナンス中です。しばらくしてもう一度お試しください。`
    await page.getByPlaceholder(notice).fill(notice)
  
    // await page.getByLabel('開始日時').fill('2025-07-23T13:04')
    // await page.getByLabel('終了日時').fill('2025-07-23T14:04')
  
    await page.getByRole('button', { name: 'メンテナンス開始' }).click()
    await page.locator('button[aria-label="Close"]').click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  
    await page.getByRole('button', { name: 'メンテナンス開始' }).click()
    await page.getByRole('button', { name: 'キャンセル' }).click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  
    await page.getByRole('button', { name: 'メンテナンス開始' }).click()
    await page.getByRole('button', { name: '有効化' }).click()
    await expect(page.getByText('メンテナンス中', { exact: true })).toBeVisible()
  })

  test('スケジュールメンテナンス終了ポップアップと無効化', async ({ page }) => {
    await page.getByRole('button', { name: 'メンテナンス終了' }).click()
    await expect(page.getByText('チャットボットのメンテナンス無効化の確認')).toBeVisible()
  
    // 1回目: ✖️閉じる
    await page.locator('button[aria-label="Close"]').click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  
    // 2回目: キャンセル
    await page.getByRole('button', { name: 'メンテナンス終了' }).click()
    await page.getByRole('button', { name: 'キャンセル' }).click()
    await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
  
    // 3回目: 無効化
    await page.getByRole('button', { name: 'メンテナンス終了' }).click()
    await page.getByRole('button', { name: '無効化' }).click()
  
    // ✅ メンテナンス終了を確認（"メンテナンス中"が消えている）
    await expect(page.locator('span:text("メンテナンス中")')).toHaveCount(0)
  })
  
  test('メンテナンスが自動で終了されること', async ({ page }) => {
    test.setTimeout(180000) // 3分に延長（全体）
    await page.locator('[data-tour="maintenance-scheduled"]').click()
  
    const notice = `ただいまメンテナンス中です。しばらくしてもう一度お試しください。`
    await page.getByPlaceholder(notice).fill(notice)
  
    // 現在時刻 + 1分後を開始、+2分後を終了として指定
    const now = new Date()
    const start = new Date(now.getTime() + 1 * 60 * 1000)
    const end = new Date(now.getTime() + 2 * 60 * 1000)
  
    const formatDateTime = (date: Date) => format(date, "yyyy-MM-dd'T'HH:mm")
  
    await page.getByLabel('開始日時').fill(formatDateTime(start))
    await page.getByLabel('終了日時').fill(formatDateTime(end))
  
    await page.getByRole('button', { name: 'メンテナンスの計画を開始' }).click()
    await page.getByRole('button', { name: '有効化' }).click()
  
    // 2分以上待機 → 自動終了が反映されるまで
    await page.waitForTimeout(130000) // 130秒 = 2分10秒
  
    await page.reload()
    await expect(page.getByRole('button', { name: 'メンテナンス開始' })).toBeVisible()
  })
})
