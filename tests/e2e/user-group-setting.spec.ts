import { test, expect, type Page } from '@playwright/test'
import {
  navigateAndWait,
  commonLogin,
  openConfirmPopup,
  assertSortIcon,
  changePageSize,
  moveToNextAndPreviousPage,
  moveToSpecificPage
} from '../utils/helpers'

// const newGroupName: string = 'auto-group-170515'
let newGroupName: string

test.describe.serial('ユーザグループ管理機能テスト', () => {
  test.beforeEach(async ({ page }) => {
    await commonLogin(page)

    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=ユーザグループ管理'))
  })

  test.describe('表示機能', () => {
    test('レイアウト表示', async ({ page }) => {
      await expect(page.getByText('ユーザグループ一覧')).toBeVisible()
      await expect(page.getByRole('button', { name: 'ユーザグループ追加' })).toBeVisible()
      await expect(page.getByPlaceholder('ユーザグループの検索')).toBeVisible()
      await expect(page.getByRole('button', { name: 'ステータス' }).first()).toBeVisible()
      await expect(page.getByRole('columnheader', { name: 'ユーザグループ名' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: '所属人数' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: '登録日時' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: '更新日時' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: 'ステータス' })).toBeVisible()
    })
  })

  test.describe('ユーザグループ追加機能', () => {
    test('ユーザグループ追加ポップアップ表示確認', async ({ page }) => {
      await page.getByRole('button', { name: 'ユーザグループ追加' }).click()

      await expect(page.getByText('新規ユーザグループ登録')).toBeVisible()
      await expect(page.getByText('新しいユーザグループを作成します。')).toBeVisible()
      await expect(page.getByPlaceholder('ユーザグループの名前')).toBeVisible()
      await expect(page.getByPlaceholder('ユーザグループの説明')).toBeVisible()
      // ステータス確認（role="switch" が存在すること）
      await expect(page.getByRole('switch')).toBeVisible()
      await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('ポップアップ閉じる（✖️ボタン）', async ({ page }) => {
      await page.getByRole('button', { name: 'ユーザグループ追加' }).click()
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ポップアップ閉じる（キャンセルボタン）', async ({ page }) => {
      await page.getByRole('button', { name: 'ユーザグループ追加' }).click()
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('必須項目未入力時のエラーメッセージ表示', async ({ page }) => {
      await page.getByRole('button', { name: 'ユーザグループ追加' }).click()
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText('ユーザグループ名は 1 文字以上必要です。')).toBeVisible()
    })

    test('正常ユーザグループ追加', async ({ page }) => {
      const now = new Date()
      const hhmmss = now.toTimeString().slice(0, 8).replace(/:/g, '') // "141503" みたいな文字列
      newGroupName = `auto-group-${hhmmss}`

      await page.getByRole('button', { name: 'ユーザグループ追加' }).click()
      await page.getByPlaceholder('ユーザグループの名前').fill(newGroupName)
      await page.getByPlaceholder('ユーザグループの説明').fill('自動テスト追加グループ')
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText(newGroupName)).toBeVisible({ timeout: 10000 })
    })
  })

  test.describe('ユーザグループ検索機能テスト', () => {
    test('存在しないキーワードでの検索結果確認', async ({ page }) => {
      await page.getByPlaceholder('ユーザグループの検索').fill('存在しないグループXYZ')
      await expect(page.getByText('データがありません')).toBeVisible({ timeout: 5000 })
    })

    test('部分一致検索ができること', async ({ page }) => {
      await page.getByPlaceholder('ユーザグループの検索').fill(newGroupName)
      await expect(page.getByText(newGroupName)).toBeVisible({ timeout: 5000 })
    })

    const statuses = ['有効', '無効']
    for (const status of statuses) {
      test(`ステータスで「${status}」を絞り込んで検索できること`, async ({ page }) => {
        await page.locator('div[role="button"]', { hasText: 'ステータス' }).first().click()
        await page.getByRole('option', { name: status, exact: true }).click()
        await page.waitForTimeout(3000)

        const rows = page.locator('tbody tr')
        const rowCount = await rows.count()

        for (let i = 0; i < rowCount; i++) {
          const statusCell = rows.nth(i).locator('td').nth(4) //  正しく5列目（0始まりなのでnth(4)）
          await expect(statusCell).toHaveText(status)
        }
      })
    }

    test('表示件数を変更できること', async ({ page }) => {
      await changePageSize(page, '5')
    })

    test('ページ送りと戻る操作ができること', async ({ page }) => {
      await moveToNextAndPreviousPage(page, '5')
    })

    test('指定ページ（2ページ目）に移動できること', async ({ page }) => {
      await moveToSpecificPage(page, '5', '2')
    })
  })

  test.describe('所属ユーザ機能テスト', () => {
    test('所属ユーザ一覧が表示されること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')

      await expect(page.getByText('所属ユーザ一覧')).toBeVisible()
      await expect(page.getByRole('button', { name: 'ユーザをグループに追加' })).toBeVisible()
      await expect(page.getByRole('button', { name: '選択したユーザを削除する' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: '名前' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: 'アカウント情報' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: '権限' })).toBeVisible()
      await expect(page.getByRole('columnheader', { name: '操作' })).toBeVisible()
    })

    test('ユーザをグループに追加するポップアップが表示されること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')
      await page.getByRole('button', { name: 'ユーザをグループに追加' }).click()

      await expect(page.getByText('ユーザをグループに追加する')).toBeVisible()
      await page.getByPlaceholder('ユーザを選択・入力してください。')
      await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('ユーザー追加ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')

      await page.getByRole('button', { name: 'ユーザをグループに追加' }).click()
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ユーザ追加ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')

      await page.getByRole('button', { name: 'ユーザをグループに追加' }).click()
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('最初の5ユーザを選択してグループに追加できること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')

      // 「ユーザをグループに追加」ボタンをクリック
      await page.getByRole('button', { name: 'ユーザをグループに追加' }).click()

      // 明確なボタンセレクタでドロップダウンを開く
      const comboboxRoot = page.locator('div[role="button"]', { hasText: 'ユーザを選択・入力してください。' })
      await comboboxRoot.locator('button').click()

      // ドロップダウンの表示を待つ
      const userOptions = page.locator('ul[role="listbox"] > li')
      await expect(userOptions.first()).toBeVisible({ timeout: 5000 })

      // 最初の6ユーザーを選択
      const count = await userOptions.count()
      const maxSelect = Math.min(count, 5)

      for (let i = 0; i < maxSelect; i++) {
        await userOptions.nth(i).click()
      }

      // ドロップダウンを閉じるよう（他の場所をクリック）
      await page.getByText('ユーザをグループに追加する').click()

      // ボタンを探す（hasText で「,」が含まれる文字列を想定）
      const selectionButton = page.locator('button', { hasText: ',' })
      await expect(selectionButton).toBeVisible({ timeout: 5000 })

      const selectedUserText = await selectionButton.innerText()
      const selectedUserIds = selectedUserText.split(',').map(str => str.trim())

      // 「確定」してから変更保存
      await page.getByRole('button', { name: '確定' }).click()
      await page.getByRole('button', { name: '変更' }).click()

      const rows = page.locator('tbody tr')

      for (const userId of selectedUserIds) {
        const match = rows.filter({
          has: page.locator(`td:nth-child(3) div >> text="${userId}"`)
        })
        await expect(match).toHaveCount(1)
      }
    })

    test('表示件数を変更できること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')
      await changePageSize(page, '5')
    })

    test('ページ送りと戻る操作ができること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')
      await moveToNextAndPreviousPage(page, '5')
    })

    test('指定ページ（2ページ目）に移動できること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')
      await moveToSpecificPage(page, '5', '2')
    })

    async function assertSortIconUserGp(page: Page, columnName: string, expectedIcon: string, clickCount = 1) {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')
      await assertSortIcon(page, columnName, expectedIcon, clickCount)
    }

    test('名前を昇順ソートできること', async ({ page }) => {
      await assertSortIconUserGp(page, '名前', 'bars-arrow-up-20-solid', 1)
    })

    test('名前を降順ソートできること', async ({ page }) => {
      await assertSortIconUserGp(page, '名前', 'bars-arrow-down-20-solid', 2)
    })

    test('アカウント情報を昇順ソートできること', async ({ page }) => {
      await assertSortIconUserGp(page, 'アカウント情報', 'bars-arrow-up-20-solid', 1)
    })

    test('アカウント情報を降順ソートできること', async ({ page }) => {
      await assertSortIconUserGp(page, 'アカウント情報', 'bars-arrow-down-20-solid', 2)
    })

    test('権限を昇順ソートできること', async ({ page }) => {
      await assertSortIconUserGp(page, '権限', 'bars-arrow-up-20-solid', 1)
    })

    test('権限を降順ソートできること', async ({ page }) => {
      await assertSortIconUserGp(page, '権限', 'bars-arrow-down-20-solid', 2)
    })

    const deleteFirstUserRow = async (page: Page) => {
      const firstRow = page.locator('table tbody tr').first()
      const deleteButton = firstRow.locator('button:has(span.iconify.i-heroicons\\:trash-20-solid)')
      await expect(deleteButton).toBeVisible({ timeout: 5000 })
      await deleteButton.click()
      return firstRow
    }

    test('所属ユーザー削除ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')

      await deleteFirstUserRow(page)
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('所属ユーザー削除ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')

      await deleteFirstUserRow(page)
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('所属ユーザーを削除できること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')
      // 最初の行の2列目のユーザー名を取得
      const firstRow = page.locator('table tbody tr').first()
      const userName = await firstRow.locator('td:nth-child(2) div div').innerText()

      // 削除ボタン押下 → モーダルで「変更」クリック
      const deleteButton = firstRow.locator('button:has(span.iconify.i-heroicons\\:trash-20-solid)')
      await deleteButton.click()
      await page.getByRole('button', { name: '変更' }).click()

      // 完全一致で削除確認
      const userCells = page.locator('tbody td:nth-child(3) > div > div:first-child')
      await expect(userCells.filter({ hasText: new RegExp(`^${userName}$`) })).toHaveCount(0)
    })

    test('複数ユーザ削除確認を✖️ボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')

      await page.locator('tbody input[type="checkbox"]').first().check()
      await page.getByRole('button', { name: '選択したユーザを削除' }).click()
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('複数ユーザ削除確認をキャンセルボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')

      await page.locator('tbody input[type="checkbox"]').first().check()
      await page.getByRole('button', { name: '選択したユーザを削除' }).click()
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('複数のユーザーを削除できること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '所属ユーザ編集')

      const rows = page.locator('tbody tr')
      const checkboxes = rows.locator('input[type="checkbox"]')

      const count = await checkboxes.count()
      const deleteCount = Math.min(2, count)

      const deletedUserNames: string[] = []

      for (let i = 0; i < deleteCount; i++) {
        const row = rows.nth(i)
        const name = await row.locator('td:nth-child(3) > div > div:first-child').innerText()
        deletedUserNames.push(name.trim())
        await checkboxes.nth(i).check()
      }

      await page.getByRole('button', { name: '選択したユーザを削除' }).click()
      await page.getByRole('button', { name: '変更' }).click()
      await page.waitForTimeout(2000)

      const userCells = page.locator('tbody td:nth-child(3) > div > div:first-child')
      for (const name of deletedUserNames) {
        await expect(userCells.filter({ hasText: new RegExp(`^${name}$`) })).toHaveCount(0)
      }
    })
  })

  test.describe('ユーザグループ無効化/有効化機能テスト', () => {
    test('ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '無効化/有効化')
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, '無効化/有効化')
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('有効状態から無効状態へ切り替えできること', async ({ page }) => {
      const row = page.locator('tr', { hasText: newGroupName })
      await expect(row.locator('td >> text=有効')).toBeVisible()

      await openConfirmPopup(page, newGroupName, '無効化/有効化')

      await expect(page.getByText('ステータス変更')).toBeVisible()
      await expect(page.getByText('このユーザグループのステータスを無効に変更しますか？')).toBeVisible()
      await page.getByRole('button', { name: '変更' }).click()

      await expect(row.getByText('無効')).toBeVisible({ timeout: 5000 })
    })

    test('無効状態から有効状態へ切り替えできること', async ({ page }) => {
      const row = page.locator('tr', { hasText: newGroupName })
      await expect(row.locator('td >> text=無効')).toBeVisible()

      await openConfirmPopup(page, newGroupName, '無効化/有効化')

      await expect(page.getByText('ステータス変更')).toBeVisible()
      await expect(page.getByText('このユーザグループのステータスを有効に変更しますか？')).toBeVisible()
      await page.getByRole('button', { name: '変更' }).click()

      await expect(row.getByText('有効')).toBeVisible({ timeout: 5000 })
    })
  })

  test.describe('ユーザグループ編集機能', () => {
    test('ユーザグループ編集ポップアップ表示確認', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, 'ユーザグループ編集')

      await expect(page.getByText('ユーザグループを編集')).toBeVisible()
      await expect(page.getByText('ユーザグループの情報を編集します。')).toBeVisible()
      await expect(page.getByPlaceholder('ユーザグループの名前')).toHaveValue(newGroupName)
      await expect(page.getByPlaceholder('ユーザグループの説明')).toHaveValue('自動テスト追加グループ')
      await expect(page.getByRole('switch')).toBeVisible()
      await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('必須項目未入力時のエラーメッセージ表示', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, 'ユーザグループ編集')

      await page.getByPlaceholder('ユーザグループの名前').fill('')
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText('ユーザグループ名は 1 文字以上必要です。')).toBeVisible()
    })

    test('ポップアップ閉じる（✖️ボタン）', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, 'ユーザグループ編集')

      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ポップアップ閉じる（キャンセルボタン）', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, 'ユーザグループ編集')

      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('正常ユーザグループ編集', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, 'ユーザグループ編集')

      const editedName = `auto-edited-${newGroupName}`
      await page.getByPlaceholder('ユーザグループの名前').fill(editedName)
      await page.getByRole('button', { name: '確定' }).click()

      await expect(page.getByText(editedName)).toBeVisible({ timeout: 5000 })
    })
  })

  test.describe('ユーザグループ削除機能テスト', () => {
    test('ユーザグループ削除ポップアップ表示確認', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, 'ユーザグループ削除')

      await expect(page.getByText('削除確認')).toBeVisible()
      await expect(page.getByText('このユーザグループを削除しますか？')).toBeVisible()
      await expect(page.getByRole('button', { name: '削除' })).toBeVisible()
      await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('ユーザグループ削除ポップアップを✖️ボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, 'ユーザグループ削除')
      await page.locator('button[aria-label="Close"]').click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('ユーザグループ削除ポップアップをキャンセルボタンで閉じられること', async ({ page }) => {
      await openConfirmPopup(page, newGroupName, 'ユーザグループ削除')
      await page.getByRole('button', { name: 'キャンセル' }).click()
      await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('正常にユーザグループを削除できること', async ({ page }) => {
      const groupForDelTest = 'gp-for-delete-test'

      await page.getByRole('button', { name: 'ユーザグループ追加' }).click()
      await page.getByPlaceholder('ユーザグループの名前').fill(groupForDelTest)
      await page.getByPlaceholder('ユーザグループの説明').fill('自動テスト追加グループ')
      await page.getByRole('button', { name: '確定' }).click()

      await page.waitForTimeout(5000)

      await openConfirmPopup(page, groupForDelTest, 'ユーザグループ削除')
      await page.getByRole('button', { name: '削除' }).click()

      await expect(page.getByText(`auto-edited-${groupForDelTest}`)).not.toBeVisible({ timeout: 5000 })
    })
  })
})
