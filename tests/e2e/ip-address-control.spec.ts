import { test, expect } from '@playwright/test'
import {
  commonLogin,
  navigateAndWait,
  openConfirmPopup,
  clickRefreshButton,
  changePageSize,
  moveToNextAndPreviousPage,
  moveToSpecificPage,
} from '../utils/helpers'

let newIPAddress: string
// const newIPAddress = `************/32`

test.describe.serial('IPアドレス制御画面', () => {
　test.beforeEach(async ({ page }) => {
    await commonLogin(page)
    await navigateAndWait(page, () => page.click('text=設定'))
    await navigateAndWait(page, () => page.click('text=IPアドレス制御'))
  })

  test('レイアウトが崩れず正しく表示されること', async ({ page }) => {
    await expect(page.getByRole('heading', { name: 'IPアドレス制御' })).toBeVisible()
    await expect(page.getByRole('button', { name: 'IP制御追加' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'IPアドレス' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '優先度' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '作成日時' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '更新日時' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: 'ステータス' })).toBeVisible()
    await expect(page.getByRole('columnheader', { name: '操作' })).toBeVisible()
  })

  test.describe('IP制御追加機能', () => {

    test('追加ポップアップ表示確認', async ({ page }) => {
        await page.getByRole('button', { name: 'IP制御追加' }).click()
        await expect(page.getByText('新規IP設定作成')).toBeVisible()
        await expect(page.getByPlaceholder('例: ***********')).toBeVisible()
        await expect(page.getByRole('button', { name: '確定' })).toBeVisible()
        await expect(page.getByRole('button', { name: 'キャンセル' })).toBeVisible()
    })

    test('✖️ボタンでポップアップを閉じられること', async ({ page }) => {
        await page.getByRole('button', { name: 'IP制御追加' }).click()
        await page.locator('button[aria-label="Close"]').click()
        await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('キャンセルボタンでポップアップを閉じられること', async ({ page }) => {
        await page.getByRole('button', { name: 'IP制御追加' }).click()
        await page.getByRole('button', { name: 'キャンセル' }).click()
        await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('必須項目未入力時のエラーメッセージ', async ({ page }) => {
        await page.getByRole('button', { name: 'IP制御追加' }).click()
        await page.getByRole('button', { name: '確定' }).click()
        await expect(page.getByText('IPアドレスが必要です。')).toBeVisible()
    })

    test('正常にIP制御を追加できること', async ({ page }) => {
        newIPAddress = `************/32`

        await page.getByRole('button', { name: 'IP制御追加' }).click()
        await page.getByPlaceholder('例: ***********').fill(newIPAddress)
        await page.getByRole('button', { name: '確定' }).click()

        await expect(page.getByText(newIPAddress)).toBeVisible({ timeout: 30000 })
    })
  })

  test.describe('IP設定編集機能', () => {
    test('編集ポップアップ表示確認', async ({ page }) => {
        await openConfirmPopup(page, newIPAddress, 'IP設定編集')
        await expect(page.getByText('IP設定を編集')).toBeVisible()
        await expect(page.getByPlaceholder('例: ***********')).toHaveValue(newIPAddress)
    })

    test('必須項目未入力時のエラーメッセージ表示', async ({ page }) => {
        await openConfirmPopup(page, newIPAddress, 'IP設定編集')
        await page.getByPlaceholder('例: ***********').fill('')
        await page.getByRole('button', { name: '確定' }).click()
        await expect(page.getByText('IPアドレスが必要です。')).toBeVisible()
    })

    test('✖️ボタンでポップアップを閉じられること', async ({ page }) => {
        await openConfirmPopup(page, newIPAddress, 'IP設定編集')
        await page.locator('button[aria-label="Close"]').click()
        await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('キャンセルボタンでポップアップを閉じられること', async ({ page }) => {
        await openConfirmPopup(page, newIPAddress, 'IP設定編集')
        await page.getByRole('button', { name: 'キャンセル' }).click()
        await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('正常に編集できること', async ({ page }) => {
        await openConfirmPopup(page, newIPAddress, 'IP設定編集')
        const newLabel = `************/32`
        newIPAddress = newLabel
        await page.getByPlaceholder('例: ***********').fill(newLabel)
        await page.getByRole('button', { name: '確定' }).click()
        await expect(page.getByText(newLabel)).toBeVisible({ timeout: 30000 })
    })
  })

  test.describe('IP設定削除機能', () => {
    test('削除ポップアップ表示確認', async ({ page }) => {
        await openConfirmPopup(page, newIPAddress, 'IP設定削除')
        await expect(page.getByText('削除の確認')).toBeVisible()
        await expect(
            page.getByText(`IPアドレス「${newIPAddress}」の設定を削除しますか？この操作は取り消せません。`)
        ).toBeVisible()
    })

    test('✖️ボタンでポップアップを閉じられること', async ({ page }) => {
        await openConfirmPopup(page, newIPAddress, 'IP設定削除')
        await page.locator('button[aria-label="Close"]').click()
        await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('キャンセルボタンでポップアップを閉じられること', async ({ page }) => {
        await openConfirmPopup(page, newIPAddress, 'IP設定削除')
        await page.getByRole('button', { name: 'キャンセル' }).click()
        await expect(page.locator('div[role="dialog"]')).toHaveCount(0)
    })

    test('正常に削除できること', async ({ page }) => {
        await openConfirmPopup(page, newIPAddress, 'IP設定削除')
        await page.getByRole('button', { name: '削除' }).click()
        await expect(page.getByText(newIPAddress)).toHaveCount(0, { timeout: 5000 })
    })
  })
  
  test('リフレッシュで一覧が最新になること', async ({ page }) => {
    await clickRefreshButton(page)
    await expect(page.getByRole('heading', { name: 'IPアドレス制御一覧' })).toBeVisible()
  })

  test('表示件数を変更できること', async ({ page }) => {
    await changePageSize(page, '5')
  })

  test('ページ送りと戻る操作ができること', async ({ page }) => {
    await moveToNextAndPreviousPage(page, '5')
  })

  test('指定ページ（2ページ目）に移動できること', async ({ page }) => {
    await moveToSpecificPage(page, '5', '2')
  })
})