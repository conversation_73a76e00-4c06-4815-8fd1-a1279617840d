import { fileURLToPath } from 'url'
import path from 'path'
import { expect, type Page } from '@playwright/test'

// ページ移動 (goto, click)のみ使用、同じ画面内で入力/クリックなどは一般的いらない
export async function navigateAndWait(page: Page, action: () => Promise<unknown>) {
  await action()
  await page.waitForTimeout(2000)
  // await page.waitForLoadState('networkidle')
}

export async function commonLogin(
  page: Page, 
  username: string = 'pnl_win_thandarlwin'
) {
  const password = 'Pnl2025!'
  // const tenantId = 'pnl_playground'
  // username = 'new-user-test2'
  // await page.goto('https://chat.dev.pnl-ai-concierge.com/auth/login')
  await page.goto('https://chat.dev.pnl-ai-concierge.com/auth/operator/login')
  // await page.getByPlaceholder('Ex: playnextlab').fill(tenantId)
  await page.getByPlaceholder('Ex: john.doe').fill(username)
  await page.getByPlaceholder('パスワードを入力してください').fill(password)
  await page.getByRole('button', { name: 'ログイン' }).click()
  await page.waitForTimeout(5000)

  // const tenantButton = page.locator('button:has-text("宮若市")')
  // await tenantButton.click()
  // await page.locator('button:has-text("宮若市")').first().click()

  // テナント選択が「トライアル」でない場合は切り替える
  const currentTenant = await page
  .locator('button[data-tour="tenants-dropdown"] span.font-semibold')
  .textContent()

  // ログイン後の現在選択されているテナントを再選択（メニューを開くため）
  if (currentTenant) {
    await page.locator(`button:has-text("${currentTenant.trim()}")`).first().click()
  } else {
    await page.locator('button:has-text("トライアル")').first().click()
  }
  await page.waitForTimeout(1000)

  // PNLプレイグラウンドを選択
  await page.locator('span', { hasText: 'PNLプレイグラウンド' }).click()
  await page.waitForTimeout(3000)

  // ❗️リロード（真っ白対策）
  await page.reload()
  await page.waitForTimeout(3000)

  // const envButton = page.locator('button:has-text("本番環境")')
  // await envButton.click()
  // await page.locator('span', { hasText: '検証' }).click()
  // await page.waitForTimeout(2000)
}

export const openConfirmPopup = async (page: Page, name: string, btn: string) => {
  const row = page.locator('table tbody tr').filter({
    has: page.locator('td', { hasText: name })
  }).first()

  await row.hover()
  await row.locator('button').click()
  await page.getByRole('menuitem', { name: btn }).click()
}

// export const openFirstRowConfirmPopup = async (page: Page, btn: string) => {
//   const firstRow = page.locator('table tbody tr').first()
//   const statusCell = firstRow.locator('td').nth(-1)

//   // まずステータス列(td)をhoverする（ここで3点ボタンが生成される）
//   await statusCell.hover()

//   // 3点ボタンが生成されるのを待つ
//   const menuButton = firstRow.locator('button.row-menu')
//   await menuButton.waitFor({ state: 'attached', timeout: 5000 }) // ここ、state:'attached'（存在するか）で待つ
//   await menuButton.waitFor({ state: 'visible', timeout: 5000 }) // さらにvisibleになるのも待つ

//   // クリック
//   await menuButton.click()

//   // メニューから指定のボタン押す
//   await page.getByRole('menuitem', { name: btn }).click()
// }

export async function assertSortIcon(
  page: Page,
  columnName: string,
  expectedIcon: string,
  clickCount = 1
) {
  const headerButton = page.getByRole('columnheader', { name: columnName, exact: true }).locator('button')
  for (let i = 0; i < clickCount; i++) {
    await headerButton.click()
    await page.waitForTimeout(500) // 安定化待ち
  }
  const icon = headerButton.locator(`span[class*="${expectedIcon}"]`)
  await expect(icon).toBeVisible({ timeout: 5000 })
  // await expect(
  //   headerButton.locator(`span.iconify.i-heroicons\\:${expectedIcon}`)
  // ).toBeVisible({ timeout: 5000 })
}

export async function clickRefreshButton(page: Page) {
  const refreshButton = page.locator('button:has(span.i-prime\\:sync)')
  await refreshButton.click()
}

export async function changePageSize(page: Page, size: string) {
  await page.getByRole('combobox').selectOption(size)
  const rows = await page.locator('table tbody tr').count()
  expect(rows).toBeLessThanOrEqual(Number(size))
}

export async function moveToNextAndPreviousPage(
  page: Page,
  size: string,
  activePageSelector = 'button.text-primary-500 span'
) {
  await page.getByRole('combobox').selectOption(size)

  const nextButton = page.locator('button[aria-label="Next"]')
  const prevButton = page.locator('button[aria-label="Prev"]')

  // 次ページボタンの存在と活性チェック
  if ((await nextButton.count()) === 0 || await nextButton.isDisabled()) {
    console.log('次ページボタンが無効です。データが1ページしかありません。')
  } else {
    await nextButton.click()
    await expect(page.locator(activePageSelector).filter({ hasText: '2' })).toBeVisible()

    // 前ページボタンの存在と活性チェック
    if ((await prevButton.count()) === 0 || await prevButton.isDisabled()) {
      console.log('前ページボタンが無効です。データが1ページしかありません。')
    } else {
      await prevButton.click()
      await expect(page.locator(activePageSelector).filter({ hasText: '1' })).toBeVisible()
    }
  }
}

export async function moveToSpecificPage(
  page: Page,
  size: string,
  pageNumber: string,
  activePageSelector = 'button.text-primary-500 span'
) {
  await page.getByRole('combobox').selectOption(size)

  const targetPage = page.getByRole('button', { name: pageNumber, exact: true })

  if (await targetPage.count() === 0) {
    console.log(`${pageNumber} ページ目ボタンが存在しません。データが1ページしかありません。`)
  } else {
    await targetPage.click()
    const activePage = page.locator(activePageSelector).filter({ hasText: pageNumber })
    await expect(activePage).toBeVisible()
  }
}

// 任意の期間をカレンダーで選択する
export async function selectDateRange(page: Page, triggerSelector = 'button:has(div:has-text("年"))') {
  const now = new Date()

  // from = 今日 - 3日、to = 今日 - 1日
  const fromDate = new Date(now)
  fromDate.setDate(now.getDate() - 3)

  const toDate = new Date(now)
  toDate.setDate(now.getDate() - 1)

  const buildLabel = (date: Date) => {
    return date.toLocaleDateString('ja-JP', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    }).replace(/月/g, '月').replace(/日/g, '日')// ← 曜日だけ残す
  }

  const fromLabel = buildLabel(fromDate) // 例: "2025年4月20日日曜日"
  const toLabel = buildLabel(toDate) // 例: "2025年4月22日火曜日"

  // console.log('🟢 fromLabel:', fromLabel)
  // console.log('🔴 toLabel:', toLabel)
  await page.locator(triggerSelector).click()
  await page.locator(`[aria-label="${fromLabel}"]`).first().click()
  await page.waitForTimeout(300)
  await page.locator(`[aria-label="${toLabel}"]`).first().hover()
  await page.locator(`[aria-label="${toLabel}"]`).first().click()
  await page.waitForTimeout(300)
}

// 指定列の検索結果がカレンダーで指定した期間内であることを検証
export async function verifySearchResultDates(
  page: Page,
  sortColumnName: string,
  dateCellSelector: string,
  format: 'jp' | 'iso' = 'jp', // ← defaultはデプロイ履歴用,
  valueType: 'text' | 'input' = 'text'
) {
  // 検索結果が空の場合はスキップ
  const noDataLocator = page.getByText('データがありません', { exact: true })
  if (await noDataLocator.isVisible()) {
    console.warn(`⚠️ 対象期間にデータがありません。テストをスキップします`)
    return
  }

  // カレンダー表示の日付は共通フォーマット
  const calendarText = await page.locator('button:has(div:has-text("年")) >> div').innerText()
  const [fromStrRaw, toStrRaw] = calendarText.split('→').map(s => s.trim())

  if (!fromStrRaw || !toStrRaw) {
    throw new Error('カレンダー日付の取得に失敗しました')
  }

  const from = new Date(fromStrRaw.replace(/年|月/g, '/').replace('日', '') + ' 00:00:00 GMT+0900')
  const to = new Date(toStrRaw.replace(/年|月/g, '/').replace('日', '') + ' 23:59:59 GMT+0900')

  const parseDate = (text: string): Date => {
    if (format === 'jp') {
      // 例：2025年04月21日 17:44:54 → 2025/04/21 17:44:54
      return new Date(text.replace(/年|月/g, '/').replace('日', ''))
    } else if (format === 'iso') {
      // 例：2025-04-23 09:44:11 → 2025-04-23T09:44:11+09:00
      return new Date(text.replace(' ', 'T') + '+09:00')
    }
    throw new Error('未対応のフォーマット')
  }

  // 昇順ソート
  await assertSortIcon(page, sortColumnName, 'bars-arrow-up-20-solid', 1)
  const firstLocator = page.locator(dateCellSelector).first()
  const firstText = valueType === 'input'
    ? await firstLocator.inputValue()
    : await firstLocator.innerText()
  const firstDate = parseDate(firstText)

  // 降順ソート
  await assertSortIcon(page, sortColumnName, 'bars-arrow-down-20-solid', 1)
  const lastLocator = page.locator(dateCellSelector).first()
  const lastText = valueType === 'input'
    ? await lastLocator.inputValue()
    : await lastLocator.innerText()
  const lastDate = parseDate(lastText)

  // const cells = await page.locator('tbody tr').first().locator('td').allInnerTexts()
  // console.log('📋 1行目の全セル：', cells)

  // console.log('🟢 from:', from)
  // console.log('🔴 to:', to)
  // console.log('🟢 fromtoISOString:', from.toISOString())
  // console.log('🔴 totoISOString:', to.toISOString())

  // console.log('⬆️ firstText:', firstText)
  // console.log('⬇️ lastText:', lastText)
  // console.log('⬆️ firstDate:', firstDate)
  // console.log('⬇️ lastDate:', lastDate)
  // console.log('⬆️ firstDatetoISOString:', firstDate.toISOString())
  // console.log('⬇️ lastDatetoISOString:', lastDate.toISOString())

  expect(firstDate >= from && firstDate <= to).toBeTruthy()
  expect(lastDate >= from && lastDate <= to).toBeTruthy()
}

export async function verifyLogItemsInRange(page: Page) {
  const calendarButton = page.locator('button', { hasText: '→' }).first()
  const calendarText = await calendarButton.locator('div').innerText()
  const parts = calendarText.split('→').map(s => s.trim())

  if (parts.length !== 2 || !parts[0] || !parts[1]) {
    throw new Error('カレンダー日付の取得に失敗しました')
  }

  const parseDate = (text: string): Date => {
    if (!text.includes('月') || !text.includes('日')) {
      throw new Error(`不正な日付形式です: "${text}"`)
    }

    const [monthStr, dayStr] = text.replace('日', '').split('月')
    if (!monthStr || !dayStr) {
      throw new Error(`月日がパースできません: "${text}"`)
    }

    const month = parseInt(monthStr.trim(), 10)
    const day = parseInt(dayStr.trim(), 10)
    return new Date(2025, month - 1, day)
  }

  const from = new Date(parseDate(parts[0]).setHours(0, 0, 0))
  const to = new Date(parseDate(parts[1]).setHours(23, 59, 59))

  const logDateLocators = page.locator('.vue-recycle-scroller__item-view div.min-w-16')
  const count = await logDateLocators.count()

  if (count === 0) {
    console.warn(`⚠️ ログが見つかりませんでした（期間: ${parts[0]} → ${parts[1]}）`)
    return
  }

  for (let i = 0; i < count; i++) {
    const dateText = await logDateLocators.nth(i).innerText()
    let parsed: Date

    if (/^\d{2}:\d{2}$/.test(dateText)) {
      // 時刻のみ表示 → 今日は確定なので、今日の日付＋時間を使う
      const [hour, minute] = dateText.split(':').map(Number)
      const today = new Date()
      parsed = new Date(today.getFullYear(), today.getMonth(), today.getDate(), hour, minute)
    } else {
      // 通常の日付（例: 4月30日）
      parsed = parseDate(dateText)
    }

    expect(parsed >= from && parsed <= to).toBeTruthy()
  }
}

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

/**
 * ファイルを指定のinputにアップロードする
 */
export async function uploadFile(page: Page, fileName: string, inputSelector = 'input[type="file"]') {
  const fullPath = path.resolve(__dirname, '../assets/', fileName)
  console.log('📄 ファイルパス:', fullPath)
  const fileInput = page.locator(inputSelector)
  await fileInput.setInputFiles(fullPath)
}

/**
 * データソースのファイルアップロード用フォーム入力を行う
 */
export async function fillFileUploadForm(page: Page, title: string, fileName: string) {
  // await page.getByRole('button', { name: 'データソースを追加' }).click()
  const titleInput = page.locator('input[placeholder="例：税金に関するデータ"]')
  await titleInput.fill(title)
  await uploadFile(page, fileName, 'input[type="file"]')
}

/**
 * プレビュー確認ボタンを押して、内容が表示されることを確認
 */
export async function checkFilePreview(page: Page) {
  await page.getByRole('button', { name: 'プレビュー' }).click()
  await expect(page.getByText('プレビューを確認')).toBeVisible()
  // プレビュー表示後に一時停止（任意）
  await page.waitForTimeout(1000)
}

/**
 * 登録フォームを送信して一覧画面でデータ表示を確認
 */
export async function submitFormAndCheckList(page: Page, expectedTitle: string) {
  await page.getByRole('button', { name: '登録' }).click()
  await page.waitForTimeout(3000)
  await expect(page.getByText('データソース')).toBeVisible()

  // 登録データが一覧に表示されていることを確認
  const rows = page.locator('tbody tr')
  const rowCount = await rows.count()
  let found = false
  for (let i = 0; i < rowCount; i++) {
    const titleCell = rows.nth(i).locator('td').nth(0) // 1列目にデータソース名がある
    const text = await titleCell.innerText()
    if (text.includes(expectedTitle)) {
      found = true
      break
    }
  }
  expect(found).toBeTruthy()
}

/**
 * 利用者用チャットボットを開いて、確認メッセージを送信（PC用）
 */
export async function askChatBotPC(page: Page) {
  // チャットが開くまで待つ
  const chatWindow = page.locator('.aiko-chat-window.vr-chat-window')
  await expect(chatWindow).toBeVisible({ timeout: 3000 })

  // メッセージ入力
  const messageBox = chatWindow.locator('textarea[placeholder="メッセージを入力してください"]')
  await expect(messageBox).toBeVisible({ timeout: 3000 })
  await messageBox.fill('宮若市の議事録日程を教えて')

  // 送信ボタンを押す
  await chatWindow.locator('button.aiko-send-button').click()
  await page.waitForTimeout(10000)
}

/**
 * 利用者用チャットボットを開いて、確認メッセージを送信（携帯用）
 */
export async function askChatBotMobile(page: Page) {
  const chatWindow = page.locator('.aiko-chat-window.fullscreen-chat')
  await expect(chatWindow).toBeVisible({ timeout: 3000 })

  // メッセージ入力
  const messageBox = chatWindow.locator('textarea[placeholder="メッセージを入力してください"]')
  await expect(messageBox).toBeVisible({ timeout: 3000 })
  await messageBox.fill('宮若市の議事録日程を教えて')

  // 送信ボタンを押す
  await chatWindow.locator('button.aiko-send-button').click()
  await page.waitForTimeout(10000)
}